package admin

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPendingCancelListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取待审核注销列表
func NewGetPendingCancelListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPendingCancelListLogic {
	return &GetPendingCancelListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPendingCancelListLogic) GetPendingCancelList(req *types.GetPendingCancelListReq) (resp *types.GetPendingCancelListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
